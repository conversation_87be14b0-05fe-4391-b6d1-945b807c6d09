import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { FeaturePdfViewerComponent } from './feature-pdf-viewer.component'
import { provideMockStore } from '@ngrx/store/testing'
import { NotificationService } from '@venio/shared/ui'
import { PdfViewerFacade } from '../+state/pdf-viewer.facade'
import { DocumentsFacade } from '@venio/feature/documents'
import { DialogService } from '@progress/kendo-angular-dialog'
import { PSPDFKit } from '../models/pspdfkit.model'

describe('FeaturePdfViewerComponent', () => {
  let component: FeaturePdfViewerComponent
  let fixture: ComponentFixture<FeaturePdfViewerComponent>

  // Mock dependencies
  const pdfViewerFacadeMock = {
    getRedactedPages: jest
      .fn()
      .mockReturnValue({ pipe: () => ({ subscribe: jest.fn() }) }),
    fetchRedactionSets: jest
      .fn()
      .mockReturnValue({ pipe: () => ({ subscribe: jest.fn() }) }),
    isAnnotationChanged: { set: jest.fn() },
  }

  const documentFacadeMock = {
    loadPDF: jest.fn(),
    loadProducedPDF: jest.fn(),
  }

  const notificationServiceMock = {
    showSuccess: jest.fn(),
    showError: jest.fn(),
    showWarning: jest.fn(),
  }

  const dialogServiceMock = {
    open: jest.fn().mockReturnValue({
      content: { instance: {} },
      result: { pipe: () => ({ subscribe: jest.fn() }) },
    }),
  }

  // Mock PSPDFKit
  const mockViewerInstance = {
    viewState: {
      get: jest.fn(),
      set: jest.fn().mockReturnValue({ set: jest.fn() }),
    },
    setViewState: jest
      .fn()
      .mockImplementation((callback) => callback(mockViewerInstance.viewState)),
    setAnnotationToolbarItems: jest.fn(),
    totalPageCount: 10,
    pageInfoForIndex: jest.fn().mockReturnValue({ width: 595, height: 842 }),
    getSelectedAnnotations: jest
      .fn()
      .mockReturnValue({ map: () => ({ toArray: () => [] }) }),
    delete: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    exportXFDF: jest.fn(),
    setAnnotationPresets: jest.fn(),
    setCurrentAnnotationPreset: jest.fn(),
    zoomIn: jest.fn(),
    zoomOut: jest.fn(),
    rotateLeft: jest.fn(),
    rotateRight: jest.fn(),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeaturePdfViewerComponent],
      providers: [
        provideMockStore({}),
        { provide: PdfViewerFacade, useValue: pdfViewerFacadeMock },
        { provide: DocumentsFacade, useValue: documentFacadeMock },
        { provide: NotificationService, useValue: notificationServiceMock },
        { provide: DialogService, useValue: dialogServiceMock },
      ],
    }).compileComponents()

    // Mock global PSPDFKit
    global.PSPDFKit = {
      load: jest.fn().mockResolvedValue(mockViewerInstance),
      unload: jest.fn(),
      InteractionMode: { PAN: 'PAN', SHAPE_RECTANGLE: 'SHAPE_RECTANGLE' },
      ZoomMode: {
        FIT_TO_WIDTH: 'FIT_TO_WIDTH',
        FIT_TO_VIEWPORT: 'FIT_TO_VIEWPORT',
        AUTO: 'AUTO',
      },
      Color: { TRANSPARENT: 'transparent' },
    } as any

    fixture = TestBed.createComponent(FeaturePdfViewerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('Zoom functionality', () => {
    beforeEach(() => {
      // Setup the component with a viewer instance
      component['viewerInstance'] = mockViewerInstance
      component['currentZoomLevel'] = PSPDFKit.ZoomMode.FIT_TO_WIDTH
    })

    it('should preserve zoom level when loading a new PDF document', fakeAsync(() => {
      // Arrange
      const customZoomLevel = 1.5
      mockViewerInstance.viewState.get.mockReturnValue(customZoomLevel)

      // Act
      component['loadPdfDocument'](false)
      tick() // Wait for promises to resolve

      // Assert
      expect(component['currentZoomLevel']).toBe(customZoomLevel)
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
    }))

    it('should update currentZoomLevel when zooming in', () => {
      // Arrange
      const newZoomLevel = 1.2
      mockViewerInstance.viewState.get.mockReturnValue(newZoomLevel)

      // Act
      component['zoomIn']()

      // Assert
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
      expect(component['currentZoomLevel']).toBe(newZoomLevel)
    })

    it('should update currentZoomLevel when zooming out', () => {
      // Arrange
      const newZoomLevel = 0.8
      mockViewerInstance.viewState.get.mockReturnValue(newZoomLevel)

      // Act
      component['zoomOut']()

      // Assert
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
      expect(component['currentZoomLevel']).toBe(newZoomLevel)
    })

    it('should set zoom to FIT_TO_WIDTH when that action is triggered', () => {
      // Act
      component.actionButtonHandler('FIT_TO_WIDTH' as any)

      // Assert
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
      expect(component['currentZoomLevel']).toBe(PSPDFKit.ZoomMode.FIT_TO_WIDTH)
    })

    it('should set zoom to FIT_TO_VIEWPORT when FIT_TO_HEIGHT action is triggered', () => {
      // Act
      component.actionButtonHandler('FIT_TO_HEIGHT' as any)

      // Assert
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
      expect(component['currentZoomLevel']).toBe(
        PSPDFKit.ZoomMode.FIT_TO_VIEWPORT
      )
    })

    it('should set zoom to AUTO when ACTUAL_SIZE action is triggered', () => {
      // Act
      component.actionButtonHandler('ACTUAL_SIZE' as any)

      // Assert
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
      expect(component['currentZoomLevel']).toBe(PSPDFKit.ZoomMode.AUTO)
    })
  })
})

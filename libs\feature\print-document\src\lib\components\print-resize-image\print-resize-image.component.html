<div class="t-flex t-flex-col t-gap-4">
  <kendo-label class="t-font-semibold" text="Resize Option"></kendo-label>
  <!-- Form Container -->
  <form [formGroup]="imageResizeOption" class="t-pl-6">
    <div class="t-flex t-items-center t-gap-2">
      <kendo-checkbox formControlName="resizeImage"></kendo-checkbox>
      <span class="t-text-sm">Resize Image</span>
    </div>
    <div class="t-flex t-flex-col t-gap-2 t-mt-4 t-ml-4">
      <div class="t-flex t-gap-2 t-mb-4">
        <kendo-label class="t-text-sm">Paper Size:</kendo-label>
        <div class="t-flex t-flex-col t-gap-2">
          <kendo-dropdownlist
            formControlName="paperSize"
            [data]="tiffPaperSizesList"
            [valuePrimitive]="true"
            [textField]="'text'"
            [valueField]="'value'"></kendo-dropdownlist>

          <!-- Resize Dimensions -->
          <div class="t-flex t-gap-4">
            <div class="t-flex t-items-center t-gap-2">
              <kendo-radiobutton
                #both
                value="BOTH"
                formControlName="dimension"></kendo-radiobutton
              ><kendo-label
                class="k-radio-label"
                [for]="both"
                text="Both"></kendo-label>
            </div>

            <div class="t-flex t-items-center t-gap-2">
              <kendo-radiobutton
                #width
                value="WIDTH"
                formControlName="dimension"></kendo-radiobutton>
              <kendo-label
                class="k-radio-label"
                [for]="width"
                text="Width"></kendo-label>
            </div>
            <div class="t-flex t-items-center t-gap-2">
              <kendo-radiobutton
                #height
                value="HEIGHT"
                formControlName="dimension"></kendo-radiobutton>
              <kendo-label
                class="k-radio-label"
                [for]="height"
                text="Height"></kendo-label>
            </div>
          </div>

          <!-- Width and Height Inputs -->
          <div class="t-flex t-flex-col t-gap-4">
            <div class="t-flex t-items-center t-gap-2">
              <kendo-label class="t-w-20">Width</kendo-label>
              <div class="t-flex t-flex-col t-gap-0">
                <kendo-numerictextbox
                  formControlName="width"
                  [value]="8.5"
                  [min]="0"
                  class="t-w-64"></kendo-numerictextbox>
                <div
                  *ngIf="imageResizeOption.get('width')?.errors?.['required']"
                  class="t-text-error t-text-xs">
                  Width is required
                </div>
                <div
                  *ngIf="imageResizeOption.get('width')?.errors?.['min']"
                  class="t-text-error t-text-xs">
                  Width must be greater than 0
                </div>
              </div>
            </div>
            <div class="t-flex t-items-center t-gap-2">
              <kendo-label class="t-w-20">Height</kendo-label>
              <div class="t-flex t-flex-col t-gap-0">
                <kendo-numerictextbox
                  formControlName="height"
                  [value]="11"
                  [min]="0"
                  class="t-w-64"></kendo-numerictextbox>

                <div
                  *ngIf="imageResizeOption.get('height')?.errors?.['required']"
                  class="t-text-error t-text-xs">
                  Height is required
                </div>
                <div
                  *ngIf="imageResizeOption.get('height')?.errors?.['min']"
                  class="t-text-error t-text-xs">
                  Height must be greater than 0
                </div>
              </div>
            </div>
          </div>

          <!-- Size Options -->
          <div class="t-flex t-items-center t-gap-4">
            <kendo-label class="t-w-20">Size</kendo-label>
            <div class="t-flex t-items-center t-gap-2">
              <kendo-radiobutton
                #inch
                formControlName="sizeUnit"
                value="INCH"
                label="Inches"></kendo-radiobutton>
              <kendo-label
                class="k-radio-label"
                [for]="inch"
                text="Inch"></kendo-label>
            </div>
            <div class="t-flex t-items-center t-gap-2">
              <kendo-radiobutton
                #pixel
                formControlName="sizeUnit"
                value="PIXEL"
                label="Pixels"></kendo-radiobutton>

              <kendo-label
                class="k-radio-label"
                [for]="pixel"
                text="Pixel"></kendo-label>
            </div>
          </div>

          <!-- Maintain Aspect Ratio -->
          <div class="t-flex t-items-center t-gap-2">
            <kendo-checkbox
              formControlName="maintainAspectRatio"></kendo-checkbox>
            <kendo-label>Maintain aspect ratio</kendo-label>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
